# Contact Form Setup Instructions

## Step 1: Create Google Apps Script

1. **Open Google Apps Script**
   - Go to [script.google.com](https://script.google.com)
   - Sign in with your Google account

2. **Create New Project**
   - Click "New project"
   - Give it a name like "Contact Form Handler"

3. **Add the Script Code**
   - Delete the default `myFunction()` code
   - Copy and paste the entire content from `google-apps-script.js`
   - Save the project (Ctrl+S or Cmd+S)

## Step 2: Configure Email Notifications (Optional)

1. **Edit Email Settings** in the script:
   ```javascript
   const EMAIL_RECIPIENT = '<EMAIL>'; // Replace with your email
   const SEND_EMAIL_NOTIFICATIONS = true; // Set to true to enable
   ```

## Step 3: Deploy as Web App

1. **Deploy the Script**
   - Click "Deploy" → "New deployment"
   - Choose type: "Web app"
   - Description: "Contact Form API"
   - Execute as: "Me"
   - Who has access: "Anyone"
   - Click "Deploy"

2. **Copy the Web App URL**
   - You'll get a URL like: `https://script.google.com/macros/s/ABC123.../exec`
   - Copy this URL

## Step 4: Update HTML Form

1. **Update the Script URL** in `index.html`:
   ```javascript
   const SCRIPT_URL = 'YOUR_WEB_APP_URL_HERE';
   ```
   Replace `YOUR_WEB_APP_URL_HERE` with the URL you copied

## Step 5: Test the Form

1. **Open your HTML file** in a browser
2. **Fill out the form** with test data
3. **Submit the form**
4. **Check your Google Drive** for a new spreadsheet called "Contact Form Submissions - Skyline Sutra Realty"

## Troubleshooting

### Common Issues:

1. **CORS Errors**
   - Make sure the Web App is deployed with "Anyone" access
   - Redeploy if you made changes to the script

2. **Form Not Submitting**
   - Check browser console for errors (F12)
   - Verify the SCRIPT_URL is correct
   - Make sure all required fields are filled

3. **Data Not Appearing in Spreadsheet**
   - Check the Apps Script execution log
   - Verify the script has permission to create/edit spreadsheets

4. **Email Notifications Not Working**
   - Make sure `SEND_EMAIL_NOTIFICATIONS` is set to `true`
   - Update `EMAIL_RECIPIENT` with your email address
   - Check Gmail spam folder

### Testing the Apps Script:

1. **Test Spreadsheet Creation**:
   - In Apps Script editor, run the `testSpreadsheetCreation()` function
   - Check the logs for the spreadsheet URL

2. **Test Form Submission**:
   - Run the `testFormSubmission()` function
   - Check if test data appears in the spreadsheet

## Features Included:

✅ **Data Collection**: All form fields saved to Google Sheets
✅ **Timestamp Tracking**: Both client and server timestamps
✅ **Error Handling**: Proper error messages and logging
✅ **Email Notifications**: Optional email alerts for new submissions
✅ **Auto-formatting**: Headers and column formatting in spreadsheet
✅ **Privacy Compliance**: Privacy policy acceptance tracking

## Data Collected:

- Submission Time
- Name
- Phone Number
- Message
- Privacy Policy Acceptance
- Server Timestamp

## Security Notes:

- The Web App URL should be kept secure
- Consider adding rate limiting for production use
- Email notifications are optional for privacy
- All data is stored in your Google Drive

## Support:

If you encounter issues:
1. Check the browser console for JavaScript errors
2. Review the Apps Script execution logs
3. Verify all URLs and permissions are correct
4. Test with a simple form submission first
