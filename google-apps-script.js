/**
 * Google Apps Script for Contact Form Data Collection - Skyline Sutra Realty
 *
 * Instructions:
 * 1. Open Google Apps Script (script.google.com)
 * 2. Create a new project
 * 3. Replace the default code with this script
 * 4. Save the project
 * 5. Deploy as Web App with execute permissions for "Anyone"
 * 6. Copy the Web App URL and update it in your HTML form
 */

function doPost(e) {
  try {
    // Parse the incoming data
    const data = JSON.parse(e.postData.contents);

    // Log the received data for debugging
    console.log('Received data:', data);

    // Get or create the spreadsheet
    const sheet = getOrCreateSheet();

    // Add the form data to the sheet
    addDataToSheet(sheet, data);

    // Optional: Send email notification
    sendEmailNotification(data);

    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({
        success: true,
        message: 'Data saved successfully'
      }))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    console.error('Error processing form submission:', error);

    // Return error response
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function doGet() {
  // Handle GET requests (for testing)
  return ContentService
    .createTextOutput(JSON.stringify({
      message: 'Contact form endpoint is working!',
      timestamp: new Date()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

function getOrCreateSheet() {
  const SHEET_NAME = 'Contact Form Submissions - Skyline Sutra Realty';

  // Try to get existing spreadsheet
  let sheet;
  try {
    // First, try to get a sheet with the name in the current script's drive
    const files = DriveApp.getFilesByName(SHEET_NAME);
    if (files.hasNext()) {
      const file = files.next();
      const spreadsheet = SpreadsheetApp.openById(file.getId());
      sheet = spreadsheet.getActiveSheet();
      console.log('Found existing spreadsheet:', spreadsheet.getUrl());
    }
  } catch (error) {
    console.log('No existing sheet found, creating new one');
  }

  // If no sheet exists, create a new one
  if (!sheet) {
    const spreadsheet = SpreadsheetApp.create(SHEET_NAME);
    sheet = spreadsheet.getActiveSheet();

    // Set up headers
    const headers = [
      'Timestamp',
      'Name',
      'Phone',
      'Message',
      'Privacy Accepted',
      'Server Time'
    ];
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);

    // Format the header row
    const headerRange = sheet.getRange(1, 1, 1, headers.length);
    headerRange.setBackground('#4285f4');
    headerRange.setFontColor('#ffffff');
    headerRange.setFontWeight('bold');

    // Set column widths for better readability
    sheet.setColumnWidth(1, 180); // Timestamp
    sheet.setColumnWidth(2, 200); // Name
    sheet.setColumnWidth(3, 150); // Phone
    sheet.setColumnWidth(4, 350); // Message
    sheet.setColumnWidth(5, 120); // Privacy
    sheet.setColumnWidth(6, 180); // Server Time

    console.log('Created new spreadsheet with ID:', spreadsheet.getId());
    console.log('Spreadsheet URL:', spreadsheet.getUrl());
  }

  return sheet;
}

function addDataToSheet(sheet, data) {
  // Prepare the row data
  const rowData = [
    data.timestamp || new Date().toISOString(),
    data.name || '',
    data.phone || '',
    data.message || '',
    data.privacy ? 'Yes' : 'No',
    new Date().toISOString() // Server timestamp
  ];

  // Add the data to the next empty row
  sheet.appendRow(rowData);

  // Auto-resize columns if needed
  sheet.autoResizeColumns(1, 6);

  console.log('Data added to sheet:', rowData);
}

function sendEmailNotification(data) {
  try {
    // Configure email settings
    const EMAIL_RECIPIENT = '<EMAIL>'; // Replace with your email
    const SEND_EMAIL_NOTIFICATIONS = false; // Set to true to enable email notifications

    if (!SEND_EMAIL_NOTIFICATIONS) {
      return;
    }

    const subject = 'New Contact Form Submission - Skyline Sutra Realty';
    const body = `
New contact form submission received:

Name: ${data.name || 'Not provided'}
Phone: ${data.phone || 'Not provided'}
Message: ${data.message || 'Not provided'}
Privacy Policy Accepted: ${data.privacy ? 'Yes' : 'No'}
Submission Time: ${data.timestamp || 'Not provided'}

This is an automated message from your contact form.
    `;

    MailApp.sendEmail(EMAIL_RECIPIENT, subject, body);
    console.log('Email notification sent to:', EMAIL_RECIPIENT);

  } catch (error) {
    console.error('Error sending email notification:', error);
    // Don't throw error - email failure shouldn't break form submission
  }
}

// Test function to create a sample sheet and add test data
function testScript() {
  try {
    console.log('Testing script...');

    // Create or get the sheet
    const sheet = getOrCreateSheet();
    console.log('Sheet created/retrieved successfully');

    // Add test data
    addTestRow();

    console.log('Test completed successfully!');
    console.log('Sheet URL:', sheet.getParent().getUrl());

  } catch (error) {
    console.error('Test failed:', error);
  }
}

function addTestRow() {
  const sheet = getOrCreateSheet();

  const testData = {
    timestamp: new Date().toISOString(),
    name: 'John Doe',
    phone: '+1234567890',
    message: 'This is a test message from the contact form.',
    privacy: true
  };

  addDataToSheet(sheet, testData);
  console.log('Test row added successfully');
}

// Function to get the sheet URL (helpful for debugging)
function getSheetUrl() {
  const sheet = getOrCreateSheet();
  return sheet.getParent().getUrl();
}

// Test function for form submission
function testFormSubmission() {
  const testData = {
    postData: {
      contents: JSON.stringify({
        name: 'Test User',
        phone: '+919876543210',
        message: 'This is a test message from Skyline Sutra Realty contact form',
        privacy: true,
        timestamp: new Date().toISOString()
      })
    }
  };

  const result = doPost(testData);
  console.log('Test result:', result.getContent());
}

// Function to clear all data (except headers) - use with caution
function clearAllData() {
  const sheet = getOrCreateSheet();
  const lastRow = sheet.getLastRow();

  if (lastRow > 1) {
    sheet.deleteRows(2, lastRow - 1);
    console.log('All data cleared except headers');
  } else {
    console.log('No data to clear');
  }
}
