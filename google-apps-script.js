/**
 * Google Apps Script for Contact Form Data Collection
 * 
 * Instructions:
 * 1. Open Google Apps Script (script.google.com)
 * 2. Create a new project
 * 3. Replace the default code with this script
 * 4. Save the project
 * 5. Deploy as Web App with execute permissions for "Anyone"
 * 6. Copy the Web App URL and update it in your HTML form
 */

function doPost(e) {
  try {
    // Parse the incoming data
    const data = JSON.parse(e.postData.contents);
    
    // Log the received data for debugging
    console.log('Received data:', data);
    
    // Get or create the spreadsheet
    const spreadsheet = getOrCreateSpreadsheet();
    const sheet = spreadsheet.getActiveSheet();
    
    // Prepare the row data
    const rowData = [
      new Date(data.timestamp || new Date()), // Timestamp
      data.name || '',                        // Name
      data.phone || '',                       // Phone
      data.message || '',                     // Message
      data.privacy ? 'Yes' : 'No',           // Privacy accepted
      new Date()                              // Server timestamp
    ];
    
    // Add the data to the spreadsheet
    sheet.appendRow(rowData);
    
    // Optional: Send email notification
    sendEmailNotification(data);
    
    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({
        success: true,
        message: 'Form submitted successfully'
      }))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('Error processing form submission:', error);
    
    // Return error response
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function doGet(e) {
  // Handle GET requests (for testing)
  return ContentService
    .createTextOutput(JSON.stringify({
      message: 'Contact Form API is running',
      timestamp: new Date()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

function getOrCreateSpreadsheet() {
  const SPREADSHEET_NAME = 'Contact Form Submissions - Skyline Sutra Realty';
  
  try {
    // Try to find existing spreadsheet
    const files = DriveApp.getFilesByName(SPREADSHEET_NAME);
    if (files.hasNext()) {
      const file = files.next();
      return SpreadsheetApp.openById(file.getId());
    }
  } catch (error) {
    console.log('Existing spreadsheet not found, creating new one');
  }
  
  // Create new spreadsheet
  const spreadsheet = SpreadsheetApp.create(SPREADSHEET_NAME);
  const sheet = spreadsheet.getActiveSheet();
  
  // Set up headers
  const headers = [
    'Submission Time',
    'Name',
    'Phone',
    'Message',
    'Privacy Policy Accepted',
    'Server Timestamp'
  ];
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Format the header row
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setBackground('#4285f4');
  headerRange.setFontColor('white');
  headerRange.setFontWeight('bold');
  
  // Auto-resize columns
  sheet.autoResizeColumns(1, headers.length);
  
  console.log('Created new spreadsheet:', spreadsheet.getUrl());
  return spreadsheet;
}

function sendEmailNotification(data) {
  try {
    // Configure email settings
    const EMAIL_RECIPIENT = '<EMAIL>'; // Replace with your email
    const SEND_EMAIL_NOTIFICATIONS = false; // Set to true to enable email notifications
    
    if (!SEND_EMAIL_NOTIFICATIONS) {
      return;
    }
    
    const subject = 'New Contact Form Submission - Skyline Sutra Realty';
    const body = `
New contact form submission received:

Name: ${data.name || 'Not provided'}
Phone: ${data.phone || 'Not provided'}
Message: ${data.message || 'Not provided'}
Privacy Policy Accepted: ${data.privacy ? 'Yes' : 'No'}
Submission Time: ${data.timestamp || 'Not provided'}

This is an automated message from your contact form.
    `;
    
    MailApp.sendEmail(EMAIL_RECIPIENT, subject, body);
    console.log('Email notification sent to:', EMAIL_RECIPIENT);
    
  } catch (error) {
    console.error('Error sending email notification:', error);
    // Don't throw error - email failure shouldn't break form submission
  }
}

// Test function - you can run this to test the spreadsheet creation
function testSpreadsheetCreation() {
  const spreadsheet = getOrCreateSpreadsheet();
  console.log('Spreadsheet URL:', spreadsheet.getUrl());
  console.log('Test completed successfully');
}

// Test function for form submission
function testFormSubmission() {
  const testData = {
    postData: {
      contents: JSON.stringify({
        name: 'Test User',
        phone: '+919876543210',
        message: 'This is a test message',
        privacy: true,
        timestamp: new Date().toISOString()
      })
    }
  };
  
  const result = doPost(testData);
  console.log('Test result:', result.getContent());
}
