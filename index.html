<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Reset and base styles for Elementor compatibility */
        .contact-form-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0;
            box-sizing: border-box;
            font-family: inherit;
        }

        .contact-form-container * {
            box-sizing: border-box;
        }

        .contact-form {
            width: 100%;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .form-inner {
            padding: 30px;
        }

        .form-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f1f1f1;
        }

        .form-title {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0 0 10px 0;
            line-height: 1.2;
        }

        .form-subtitle {
            font-size: 16px;
            color: #007cba;
            margin: 0;
            cursor: pointer;
            text-decoration: underline;
            transition: color 0.3s ease;
        }

        .form-subtitle:hover {
            color: #005a87;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-group:last-of-type {
            margin-bottom: 25px;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            font-family: inherit;
            background: #f8f9fa;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #007cba;
            background: #ffffff;
            box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
        }

        .form-input::placeholder {
            color: #6c757d;
            opacity: 0.8;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 25px;
        }

        .checkbox-input {
            width: 18px;
            height: 18px;
            margin: 0;
            cursor: pointer;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .checkbox-label {
            font-size: 14px;
            color: #495057;
            line-height: 1.5;
            cursor: pointer;
            flex: 1;
        }

        .checkbox-label a {
            color: #007cba;
            text-decoration: none;
        }

        .checkbox-label a:hover {
            text-decoration: underline;
        }

        .submit-btn {
            width: 100%;
            padding: 18px 24px;
            background: #F6D13D;
            color: #2c3e50;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            outline: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .submit-btn:active {
            transform: translateY(1px);
        }

        .submit-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .action-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .whatsapp-btn {
            background: #25d366;
            color: white;
        }

        .whatsapp-btn:hover {
            background: #128c7e;
        }

        .call-btn {
            background: #007cba;
            color: white;
        }

        .call-btn:hover {
            background: #005a87;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #007cba;
        }

        .success-message {
            display: none;
            padding: 15px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            color: #155724;
            margin-bottom: 20px;
            text-align: center;
        }

        .error-message {
            display: none;
            padding: 15px;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            color: #721c24;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .form-inner {
                padding: 20px;
            }
            
            .form-input {
                padding: 12px 16px;
                font-size: 16px; /* Prevent zoom on iOS */
            }
            
            .submit-btn {
                padding: 16px 20px;
            }

            .form-title {
                font-size: 24px;
            }

            .form-subtitle {
                font-size: 14px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 10px;
            }

            .action-btn {
                width: 100%;
            }
        }

        /* Elementor-specific adjustments */
        .elementor-widget-container .contact-form-container {
            margin: 0;
        }

        .elementor-element .contact-form-container {
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="contact-form-container">
        <div class="contact-form">
            <div class="form-inner">
                <div class="form-header">
                    <h2 class="form-title">Skyline Sutra Realty</h2>
                    <p class="form-subtitle" onclick="window.open('https://skylinesutrarealty.in/listings/', '_blank')">
                        See All Listings
                    </p>
                </div>

                <div class="success-message" id="successMessage">
                    Thank you! Your message has been sent successfully.
                </div>
                <div class="error-message" id="errorMessage">
                    Sorry, there was an error sending your message. Please try again.
                </div>
                
                <form id="contactForm">
                    <div class="form-group">
                        <input type="text" class="form-input" name="name" placeholder="Name *" required>
                    </div>
                    
                    <div class="form-group">
                        <input type="tel" class="form-input" name="phone" placeholder="Phone" >
                    </div>
                    
                    <div class="form-group">
                        <textarea class="form-input form-textarea" name="message" placeholder="Message *" required></textarea>
                    </div>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" class="checkbox-input" id="privacy" name="privacy" required>
                        <label class="checkbox-label" for="privacy">
                            I accept the <a href="#" target="_blank">privacy policy</a>
                        </label>
                    </div>
                    
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-paper-plane"></i>
                        Send Message
                    </button>
                </form>

                <div class="action-buttons">
                    <a href="https://wa.me/+919876543210" class="action-btn whatsapp-btn" target="_blank">
                        <i class="fab fa-whatsapp"></i>
                        WhatsApp
                    </a>
                    <a href="tel:+919876543210" class="action-btn call-btn">
                        <i class="fas fa-phone"></i>
                        Call Now
                    </a>
                </div>
                
                <div class="loading" id="loading">
                    Sending your message...
                </div>
            </div>
        </div>
    </div>

    <script>
        // Replace with your Google Apps Script Web App URL
        const SCRIPT_URL = 'https://script.google.com/macros/s/AKfycbx_LjjzQ965GLkA5LgbpYEf2VcqhiLNKXLM-pAwSJGkXiwqIihWTVtbqJTmzlYHs0uLdw/exec';

        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const form = e.target;
            const formData = new FormData(form);
            const submitBtn = form.querySelector('.submit-btn');
            const loading = document.getElementById('loading');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            
            // Hide previous messages
            successMessage.style.display = 'none';
            errorMessage.style.display = 'none';
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            loading.style.display = 'block';
            
            // Convert FormData to regular object
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // Add timestamp
            data.timestamp = new Date().toISOString();
            
            // Send to Google Apps Script
            fetch(SCRIPT_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                loading.style.display = 'none';

                if (result.success) {
                    successMessage.style.display = 'block';
                    form.reset();
                    // Scroll to success message
                    successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                } else {
                    throw new Error(result.error || 'Unknown error occurred');
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                errorMessage.style.display = 'block';
                console.error('Error:', error);
                // Scroll to error message
                errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Message';
            });
        });
    </script>
</body>
</html>